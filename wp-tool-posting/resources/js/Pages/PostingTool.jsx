import React, { useState } from 'react';
import route from 'ziggy-js';
import { useForm } from '@inertiajs/react';
import AuthenticatedLayout from '../Layouts/AuthenticatedLayout';
import PrimaryButton from '../Components/PrimaryButton';
import SecondaryButton from '../Components/SecondaryButton';

export default function PostingTool({ auth, categories = [] }) {
    const [urlsInput, setUrlsInput] = useState('');
    const [jobs, setJobs] = useState([]);
    const [loading, setLoading] = useState(false);

    // Function to parse AI response and extract clean title and content
    const parseAIResponse = (title, content) => {
        let cleanTitle = title || '';
        let cleanContent = content || '';

        // Clean up title - remove asterisks and extra formatting
        cleanTitle = cleanTitle.replace(/^\*+\s*/, '').replace(/\s*\*+$/, '').trim();

        // If title is empty or just asterisks, try to extract from content
        if (!cleanTitle || cleanTitle === '**') {
            const titleMatch = cleanContent.match(/Title:\s*\n\s*(.+?)(?:\n\s*Content:|$)/is);
            if (titleMatch) {
                cleanTitle = titleMatch[1].trim();
            }
        }

        // Clean up content - remove title/content markers
        cleanContent = cleanContent.replace(/^\*\*Title:\*\*.*?\n/i, '');
        cleanContent = cleanContent.replace(/Title:\s*\n\s*.+?\n/i, '');
        cleanContent = cleanContent.replace(/^\*\*Content:\*\*\s*\n?/i, '');
        cleanContent = cleanContent.replace(/^Content:\s*\n?/i, '');
        cleanContent = cleanContent.trim();

        return { cleanTitle, cleanContent };
    };

    const fetchJobs = async () => {
        const urls = urlsInput
            .split('\n')
            .map((u) => u.trim())
            .filter(Boolean);
        if (urls.length === 0) return;

        setLoading(true);
        try {
            const res = await window.axios.post(route('posting-tool.fetch'), { urls });
            setJobs(
                res.data.jobs.map((job) => {
                    // Parse the AI response to get clean title and content
                    const { cleanTitle, cleanContent } = parseAIResponse(job.title, job.description);

                    return {
                        ...job,
                        title: cleanTitle,
                        category_ids: [],
                        content: cleanContent,
                        date: '',
                    };
                }),
            );
        } catch (e) {
            console.error(e);
            alert('Failed to fetch jobs');
        } finally {
            setLoading(false);
        }
    };

    const postJobs = async () => {
        setLoading(true);
        try {
            await window.axios.post(route('posting-tool.post'), { jobs });
            alert('Draft posts created!');
        } catch (e) {
            console.error(e);
            alert('Failed to post jobs');
        } finally {
            setLoading(false);
        }
    };

    const toggleCategory = (jobIndex, categoryId) => {
        setJobs((prev) => {
            const updated = [...prev];
            const ids = new Set(updated[jobIndex].category_ids);
            if (ids.has(categoryId)) ids.delete(categoryId);
            else ids.add(categoryId);
            updated[jobIndex].category_ids = Array.from(ids);
            return updated;
        });
    };

    const updateJobField = (index, field, value) => {
        setJobs((prev) => {
            const updated = [...prev];
            updated[index] = { ...updated[index], [field]: value };
            return updated;
        });
    };

    return (
        <AuthenticatedLayout user={auth.user}>
            <div className="max-w-7xl mx-auto p-6 lg:p-8">
                <h2 className="text-2xl font-bold mb-4">WordPress Posting Tool</h2>

                {/* URL Input */}
                <div className="mb-6">
                    <label className="block font-medium mb-2">Job URLs (one per line)</label>
                    <textarea
                        value={urlsInput}
                        onChange={(e) => setUrlsInput(e.target.value)}
                        className="w-full border rounded p-2 h-32"
                    />
                    <PrimaryButton className="mt-2" onClick={fetchJobs} disabled={loading}>
                        {loading ? 'Fetching…' : 'Fetch Jobs'}
                    </PrimaryButton>
                </div>

                {/* Jobs Preview */}
                {jobs.length > 0 && (
                    <div className="mb-6">
                        <h3 className="text-xl font-semibold mb-4">Job Preview</h3>
                        <div className="space-y-4">
                            {jobs.map((job, idx) => (
                                <div key={idx} className="border rounded p-4 space-y-3">
                                    <div className="text-sm text-gray-500 mb-1">{job.url}</div>

                                    <label className="block font-medium text-sm">Title</label>
                                    <input
                                        type="text"
                                        value={job.title}
                                        onChange={(e) => updateJobField(idx, 'title', e.target.value)}
                                        className="w-full border rounded p-2"
                                    />

                                    <label className="block font-medium text-sm mt-2">Content</label>
                                    <textarea
                                        value={job.content}
                                        onChange={(e) => updateJobField(idx, 'content', e.target.value)}
                                        className="w-full border rounded p-2 h-24"
                                    />

                                    <label className="block font-medium text-sm mt-2">Publish Date (optional)</label>
                                    <input
                                        type="datetime-local"
                                        value={job.date || ''}
                                        onChange={(e) => updateJobField(idx, 'date', e.target.value)}
                                        className="border rounded p-2"
                                    />
                                    <div className="font-semibold">{job.title}</div>
                                    <div className="text-sm text-gray-500 mb-2">{job.url}</div>

                                    <label className="block font-medium mb-1">Categories</label>
                                    <div className="flex flex-wrap gap-2">
                                        {categories.map((cat) => (
                                            <button
                                                key={cat.id}
                                                type="button"
                                                onClick={() => toggleCategory(idx, cat.id)}
                                                className={`px-2 py-1 rounded text-sm border ${job.category_ids.includes(cat.id) ? 'bg-indigo-600 text-white' : 'bg-white'}`}
                                            >
                                                {cat.name}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                        <PrimaryButton className="mt-4" onClick={postJobs} disabled={loading}>
                            {loading ? 'Posting…' : 'Create Drafts'}
                        </PrimaryButton>
                    </div>
                )}
            </div>
        </AuthenticatedLayout>
    );
}
