<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WordPressService
{
    protected string $baseUrl;
    protected string $username;
    protected string $password;

    public function __construct()
    {
        $this->baseUrl = rtrim(config('services.wordpress.base_url'), '/');
        $this->username = config('services.wordpress.username');
        $this->password = config('services.wordpress.password');
    }

    protected function client()
    {
        return Http::withBasicAuth($this->username, $this->password)
            ->acceptJson();
    }

    /**
     * Fetch all WordPress categories.
     *
     * @return array<int, array<string,mixed>>
     */
    public function getCategories(): array
    {
        $response = $this->client()->get("{$this->baseUrl}/wp-json/wp/v2/categories", [
            'per_page' => 100,
        ]);

        if ($response->failed()) {
            Log::error('Failed to fetch WP categories', ['response' => $response->body()]);
            return [];
        }

        return $response->json();
    }

    /**
     * Create a draft post in WordPress.
     *
     * @param string $title
     * @param string $content
     * @param array<int> $categoryIds
     * @return array<string,mixed>|null
     */
    public function createDraftPost(string $title, string $content, array $categoryIds = [], ?string $date = null): ?array
    {
        $payload = [
            'title'      => $title,
            'content'    => $content,
            'status'     => 'draft',
        ];

        if ($date) {
            // WordPress expects local timezone date in ISO 8601 (YYYY-MM-DDTHH:MM:SS)
            $payload['date'] = $date;
        }

        if (!empty($categoryIds)) {
            $payload['categories'] = $categoryIds;
        }

        Log::info('Creating WordPress post', [
            'url' => "{$this->baseUrl}/wp-json/wp/v2/posts",
            'payload' => $payload,
            'auth_configured' => !empty($this->username) && !empty($this->password)
        ]);

        $response = $this->client()->post("{$this->baseUrl}/wp-json/wp/v2/posts", $payload);

        if ($response->failed()) {
            Log::error('Failed to create WP post', [
                'status' => $response->status(),
                'response' => $response->body(),
                'headers' => $response->headers()
            ]);
            return null;
        }

        $result = $response->json();
        Log::info('WordPress post created successfully', [
            'post_id' => $result['id'] ?? 'unknown',
            'status' => $result['status'] ?? 'unknown',
            'title' => $result['title']['rendered'] ?? 'unknown'
        ]);

        return $result;
    }
}
