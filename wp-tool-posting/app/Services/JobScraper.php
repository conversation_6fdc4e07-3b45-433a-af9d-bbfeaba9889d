<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use DOMXPath;

class JobScraper
{
    public function __construct(private OpenAIService $ai)
    {
    }
    
    /**
     * Scrape basic title and content from a URL.
     * Note: This is a best-effort generic scraper. It can be replaced with
     * site-specific logic later.
     *
     * @return array{title:string|null, content:string|null}
     */
    public function scrape(string $url): array
    {
        try {
            Log::info('Starting job scrape', ['url' => $url]);
            
            // Fetch the URL content
            $response = Http::timeout(15)->get($url);
            if ($response->failed()) {
                Log::warning('Failed to fetch URL', [
                    'url' => $url, 
                    'status' => $response->status()
                ]);
                return ['title' => null, 'content' => null];
            }
            
            $html = $response->body();
            if (empty($html)) {
                Log::warning('Empty HTML response', ['url' => $url]);
                return ['title' => null, 'content' => null];
            }
            
            Log::info('Successfully fetched HTML', [
                'url' => $url,
                'html_length' => strlen($html)
            ]);
            
            // Verify OpenAI API key is set
            $apiKey = config('services.openai.key');
            if (empty($apiKey)) {
                Log::error('OpenAI API key is not set. Check your .env file for OPENAI_API_KEY');
                return ['title' => null, 'content' => null];
            }
            
            // Try OpenAI extraction with web search if possible
            Log::info('Attempting OpenAI extraction with web search');
            try {
                // First try with web search capability
                [$aiTitle, $aiContent] = $this->ai->extractWithWebSearch($html, $url);
                Log::info('Used web search extraction method');
            } catch (\Exception $e) {
                // Fallback to regular extraction if web search fails or is not available
                Log::warning('Web search extraction failed, falling back to regular extraction', ['error' => $e->getMessage()]);
                [$aiTitle, $aiContent] = $this->ai->extract($html);
            }
            
            // Log the extraction results
            if ($aiTitle && $aiContent) {
                Log::info('OpenAI extraction successful', [
                    'title_length' => strlen($aiTitle),
                    'content_length' => strlen($aiContent)
                ]);
                return ['title' => $aiTitle, 'content' => $aiContent];
            } elseif ($aiTitle) {
                Log::warning('OpenAI extracted title but no content', ['title' => $aiTitle]);
                return ['title' => $aiTitle, 'content' => null];
            } elseif ($aiContent) {
                Log::warning('OpenAI extracted content but no title', ['content_length' => strlen($aiContent)]);
                return ['title' => null, 'content' => $aiContent];
            }
            
            Log::warning('OpenAI extraction failed to return title or content');
            
            // If OpenAI failed, try fallback DOM extraction
            libxml_use_internal_errors(true);
            $dom = new DOMDocument();
            $dom->loadHTML($html, LIBXML_NOWARNING | LIBXML_NOERROR);
            $xpath = new DOMXPath($dom);
            
            // Try to extract title from common elements
            $title = $this->extractTitleFromDOM($xpath);
            $content = $this->extractContentFromDOM($xpath);
            
            if ($title || $content) {
                Log::info('Fallback DOM extraction successful', [
                    'title_found' => !empty($title),
                    'content_found' => !empty($content)
                ]);
                return ['title' => $title, 'content' => $content];
            }
            
            Log::warning('All extraction methods failed');
            return ['title' => null, 'content' => null];
        } catch (\Throwable $e) {
            Log::error('Job scrape failed with exception', [
                'url' => $url, 
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['title' => null, 'content' => null];
        }
    }
    
    /**
     * Extract title from DOM using common selectors
     */
    private function extractTitleFromDOM(DOMXPath $xpath): ?string
    {
        // Try common title selectors
        $titleSelectors = [
            '//h1[contains(@class, "job-title")]',
            '//h1[contains(@class, "posting-title")]',
            '//h1[contains(@class, "position-title")]',
            '//div[contains(@class, "job-title")]//h1',
            '//div[contains(@class, "job-header")]//h1',
            '//h1',
        ];
        
        foreach ($titleSelectors as $selector) {
            $nodes = $xpath->query($selector);
            if ($nodes && $nodes->length > 0) {
                $title = trim($nodes->item(0)->textContent);
                if (!empty($title)) {
                    return $title;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Extract content from DOM using common selectors
     */
    private function extractContentFromDOM(\DOMXPath $xpath): ?string
    {
        // Try common job description selectors
        $contentSelectors = [
            "//div[contains(@class, 'job-description')]",
            "//div[contains(@class, 'description')]",
            "//div[contains(@class, 'posting-body')]",
            "//div[contains(@class, 'job-details')]",
            "//article",
            "//main",
            "//div[contains(@class, 'content')]",
        ];
        
        foreach ($contentSelectors as $selector) {
            $elements = $xpath->query($selector);
            if ($elements && $elements->length > 0) {
                // Get the raw text content
                $rawContent = $elements->item(0)->textContent;
                
                // Clean up the content
                $content = $this->cleanupContent($rawContent);
                
                return $content;
            }
        }
        
        return null;
    }
    
    /**
     * Clean up extracted content to make it more readable
     */
    private function cleanupContent(string $content): string
    {
        // Remove excessive whitespace
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Remove any non-printable characters
        $content = preg_replace('/[\x00-\x1F\x7F]/', '', $content);
        
        // Split content into paragraphs based on common patterns
        $patterns = [
            '/About the role:/', '/What you\'ll do:/', '/What we\'re looking for:/',
            '/Requirements:/', '/Responsibilities:/', '/Qualifications:/',
            '/About us:/', '/Benefits:/', '/Skills:/', '/Experience:/',
        ];
        
        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, "\n\n$0\n", $content);
        }
        
        // Clean up any remaining formatting issues
        $content = trim($content);
        
        return $content;
    }
}
