<?php

namespace App\Services;

use OpenAI;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class OpenAIService
{
    private $apiKey;
    private $model;
    private $client;

    public function __construct()
    {
        $this->apiKey = Config::get('services.openai.key');
        $this->model = Config::get('services.openai.model', 'gpt-4o-mini');
        
        // Initialize the OpenAI client
        $this->client = OpenAI::client($this->apiKey);
    }

    /**
     * Extract title and content from raw HTML using OpenAI.
     * Returns [title, content] array (nulls if failure)
     */
    public function extract(string $html): array
    {
        if (empty($this->apiKey)) {
            Log::error('OpenAI API key is not configured');
            return [null, null];
        }

        // Trim very large HTML to reduce token usage
        $htmlSnippet = substr($html, 0, 20000);

        $prompt = "Extract the job title and job description from the URL. Return ONLY in this exact format:

Title = [job title here]
Content = [job description here]

Do not add any extra formatting, asterisks, or markdown. Just the plain text.";

        try {
            Log::info('Calling OpenAI API', ['model' => $this->model, 'html_length' => strlen($htmlSnippet)]);
            
            $response = $this->client->chat()->create([
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $prompt
                    ],
                    [
                        'role' => 'user',
                        'content' => $htmlSnippet
                    ]
                ],
                'max_tokens' => 1000
            ]);
            
            if (isset($response->choices[0]->message->content)) {
                $content = $response->choices[0]->message->content;

                Log::info('OpenAI raw response', ['content' => substr($content, 0, 500)]);

                // First try to parse as JSON
                $jsonText = $this->extractJsonFromText($content);
                $json = json_decode($jsonText, true);

                if (json_last_error() === JSON_ERROR_NONE && isset($json['title']) && isset($json['content'])) {
                    $title = $json['title'];
                    $jobContent = $json['content'];

                    if (!empty($title) && !empty($jobContent)) {
                        Log::info('OpenAI extraction successful (JSON format)', [
                            'title_length' => strlen($title),
                            'content_length' => strlen($jobContent)
                        ]);
                        return [$title, $jobContent];
                    }
                }

                // If JSON parsing failed, try to extract from formatted text
                $title = null;
                $jobContent = null;

                // Look for "Title = " followed by the actual title
                if (preg_match('/Title\s*=\s*(.+?)(?:\n|$)/i', $content, $matches)) {
                    $title = trim($matches[1]);
                }

                // Look for "Content = " followed by the actual content
                if (preg_match('/Content\s*=\s*(.*)/is', $content, $matches)) {
                    $jobContent = trim($matches[1]);
                }

                if (!empty($title) && !empty($jobContent)) {
                    Log::info('OpenAI extraction successful (text format)', [
                        'title_length' => strlen($title),
                        'content_length' => strlen($jobContent)
                    ]);
                    return [$title, $jobContent];
                }

                Log::warning('OpenAI extraction returned empty title and content');
            } else {
                Log::warning('OpenAI API response missing content');
            }
        } catch (\Exception $e) {
            Log::error('OpenAI API request exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [null, null];
        }

        // Return default values if extraction failed
        return [null, null];
    }
    
    /**
     * Try to extract JSON from text that might contain additional content
     */
    private function extractJsonFromText(string $text): string
    {
        // Remove any markdown code block formatting
        $text = preg_replace('/```json\s*/', '', $text);
        $text = preg_replace('/```\s*$/', '', $text);

        // Look for JSON object pattern - find the first { and match to the last }
        if (preg_match('/\{.*\}/s', $text, $matches)) {
            return $matches[0];
        }

        // If no JSON found, try to find content between quotes that might be JSON-like
        if (preg_match('/"title".*?"content"/s', $text)) {
            // Try to construct a simple JSON from the text
            $title = '';
            $content = '';

            if (preg_match('/"title"\s*:\s*"([^"]+)"/i', $text, $titleMatch)) {
                $title = $titleMatch[1];
            }

            if (preg_match('/"content"\s*:\s*"([^"]+)"/i', $text, $contentMatch)) {
                $content = $contentMatch[1];
            }

            if ($title || $content) {
                return json_encode(['title' => $title, 'content' => $content]);
            }
        }

        return $text;
    }
    
    /**
     * Extract title and content from raw HTML using OpenAI with web search capability.
     * Returns [title, content] array (nulls if failure)
     */
    public function extractWithWebSearch(string $html, string $url): array
    {
        if (empty($this->apiKey)) {
            Log::error('OpenAI API key is not configured');
            return [null, null];
        }

        // Trim very large HTML to reduce token usage
        $htmlSnippet = substr($html, 0, 15000);

        try {
            Log::info('Calling OpenAI API with web search', ['model' => $this->model, 'url' => $url]);
            
            // Debug: Check if responses() method exists
            if (!method_exists($this->client, 'responses')) {
                Log::warning('The OpenAI client does not have a responses() method, falling back to regular extraction');
                return $this->extract($html);
            }
            
            $response = $this->client->responses()->create([
                'model' => $this->model,
                'tools' => [
                    [
                        'type' => 'web_search_preview'
                    ]
                ],
                'input' => "{$url}\n\nGive me just these response:\n\nTitle = The job title\nContent = The job description\n\nGive me full content job description. Dont modify just copy and paste. DONT INCLUDE THE FORMS. DO NOT INCLUDE JOB ID or anything else in the content/job description.",
                'max_output_tokens' => 10000,
                'tool_choice' => 'auto',
                'parallel_tool_calls' => true
            ]);
            
            $outputText = '';
            $title = null;
            $content = null;
            
            // Process the response
            foreach ($response->output as $output) {
                if ($output->type === 'message') {
                    foreach ($output->content as $content) {
                        if ($content->type === 'output_text') {
                            $outputText .= $content->text;
                        }
                    }
                }
            }
            
            // Extract title and content from the output
            if (!empty($outputText)) {
                // Try to find the job title
                if (preg_match('/(?:Job Title|Title|Position):\s*([^\n]+)/i', $outputText, $matches)) {
                    $title = trim($matches[1]);
                } elseif (preg_match('/^([^\n]{5,100})\n/i', $outputText, $matches)) {
                    // Fallback: use first line if it looks like a title
                    $title = trim($matches[1]);
                }
                
                // Use the rest as content, or the whole text if no title was found
                if ($title) {
                    $content = trim(str_replace($title, '', $outputText));
                } else {
                    $content = $outputText;
                    
                    // Try to extract a title from the content
                    $lines = explode("\n", $content);
                    if (!empty($lines[0]) && strlen($lines[0]) < 100) {
                        $title = trim($lines[0]);
                        $content = trim(implode("\n", array_slice($lines, 1)));
                    }
                }
                
                if (!empty($title) && !empty($content)) {
                    Log::info('OpenAI web search extraction successful', [
                        'title_length' => strlen($title),
                        'content_length' => strlen($content)
                    ]);
                    return [$title, $content];
                }
            }
            
            Log::warning('OpenAI web search extraction returned empty title or content');
        } catch (\Exception $e) {
            Log::error('OpenAI web search API request exception', [
                'error' => $e->getMessage(), 
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        // Fallback to regular extraction if web search fails
        Log::info('Falling back to regular extraction after web search failure');
        return $this->extract($html);
    }
}
