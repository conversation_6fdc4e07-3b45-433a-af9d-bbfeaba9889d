<?php

namespace App\Http\Controllers;

use App\Http\Requests\FetchJobsRequest;
use App\Services\WordPressService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Inertia\Inertia;

class PostingToolController extends Controller
{
    public function __construct(private WordPressService $wordpress, private \App\Services\JobScraper $scraper)
    {
    }

    /**
     * Display the main posting tool page.
     */
    public function index()
    {
        $categories = $this->wordpress->getCategories();
        $authors = $this->wordpress->getAuthors();

        return Inertia::render('PostingTool', [
            'categories' => $categories,
            'authors' => $authors,
        ]);
    }

    /**
     * Fetch job details from provided URLs.
     * NOTE: Currently a stub implementation that returns minimal info.
     */
    public function fetch(FetchJobsRequest $request)
    {
        $urls = $request->validated('urls');

        $results = collect($urls)->map(function (string $url) {
            $scraped = $this->scraper->scrape($url);
            return [
                'url' => $url,
                'title' => $scraped['title'] ?? ($url),
                'company' => null,
                'description' => $scraped['content'],
                'location' => null,
            ];
        })->values();

        return response()->json(['jobs' => $results]);
    }

    /**
     * Create draft posts on WordPress.
     */
    public function postJobs(Request $request)
    {
        $jobs = $request->validate([
            'jobs' => 'required|array',
            'jobs.*.title' => 'required|string',
            'jobs.*.content' => 'nullable|string',
            'jobs.*.category_ids' => 'array',
            'jobs.*.category_ids.*' => 'integer',
            'jobs.*.date' => 'nullable|date',
            'jobs.*.author_id' => 'nullable|integer',
        ])['jobs'];

        $results = collect($jobs)->map(function (array $job) {
            // Clean up the date field - only pass non-empty dates
            $date = Arr::get($job, 'date');
            if (empty($date) || $date === '') {
                $date = null;
            }

            $result = $this->wordpress->createDraftPost(
                Arr::get($job, 'title'),
                Arr::get($job, 'content', ''),
                Arr::get($job, 'category_ids', []),
                $date,
                Arr::get($job, 'author_id')
            );

            return [
                'title' => Arr::get($job, 'title'),
                'success' => $result !== null,
                'post_data' => $result
            ];
        });

        $successful = $results->where('success', true);
        $failed = $results->where('success', false);

        if ($failed->count() > 0) {
            \Illuminate\Support\Facades\Log::warning('Some WordPress posts failed to create', [
                'successful_count' => $successful->count(),
                'failed_count' => $failed->count(),
                'failed_titles' => $failed->pluck('title')->toArray()
            ]);
        }

        return response()->json([
            'created' => $successful->pluck('post_data'),
            'successful_count' => $successful->count(),
            'failed_count' => $failed->count(),
            'message' => $successful->count() > 0
                ? "Successfully created {$successful->count()} draft posts!"
                : 'Failed to create any draft posts. Check logs for details.',
        ]);
    }
}
