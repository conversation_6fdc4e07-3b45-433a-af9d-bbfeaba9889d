<?php

namespace App\Http\Controllers;

use App\Http\Requests\FetchJobsRequest;
use App\Services\WordPressService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Inertia\Inertia;

class PostingToolController extends Controller
{
    public function __construct(private WordPressService $wordpress, private \App\Services\JobScraper $scraper)
    {
    }

    /**
     * Display the main posting tool page.
     */
    public function index()
    {
        $categories = $this->wordpress->getCategories();

        return Inertia::render('PostingTool', [
            'categories' => $categories,
        ]);
    }

    /**
     * Fetch job details from provided URLs.
     * NOTE: Currently a stub implementation that returns minimal info.
     */
    public function fetch(FetchJobsRequest $request)
    {
        $urls = $request->validated('urls');

        $results = collect($urls)->map(function (string $url) {
            $scraped = $this->scraper->scrape($url);
            return [
                'url' => $url,
                'title' => $scraped['title'] ?? ($url),
                'company' => null,
                'description' => $scraped['content'],
                'location' => null,
            ];
        })->values();

        return response()->json(['jobs' => $results]);
    }

    /**
     * Create draft posts on WordPress.
     */
    public function postJobs(Request $request)
    {
        $jobs = $request->validate([
            'jobs' => 'required|array',
            'jobs.*.title' => 'required|string',
            'jobs.*.content' => 'nullable|string',
            'jobs.*.category_ids' => 'array',
            'jobs.*.category_ids.*' => 'integer',
            'jobs.*.date' => 'nullable|date',
        ])['jobs'];

        $created = collect($jobs)->map(function (array $job) {
            return $this->wordpress->createDraftPost(
                Arr::get($job, 'title'),
                Arr::get($job, 'content', ''),
                Arr::get($job, 'category_ids', []),
                Arr::get($job, 'date')
            );
        })->filter();

        return response()->json([
            'created' => $created,
            'message' => 'Draft posts created successfully!',
        ]);
    }
}
